import matplotlib.pyplot as plt
import matplotlib as mpl
import numpy as np

# 攻击类型
attack_types = ['CPO', 'RPO', 'DoS', 'GS']
# 攻击密度 (%)
densities = [40, 50, 60]

# --- DCRAD (我们的模型) ---
# 基线F1: CPO(97.13), RPO(97.07), DoS(97.57), GS(97.52)
# 表现: 严格按照您的要求，在DoS/GS的60%密度下降到85左右。CPO/RPO则平缓下降。
dcrad = [
    [96.15, 94.21, 92.05],  # CPO: 衰减平缓，但起点更低
    [96.08, 94.02, 91.89],  # RPO: 衰减平缓，但起点更低
    [94.05, 91.88, 87.34],  # DoS: 衰减剧烈，终点锚定在 ~85
    [93.58, 89.32, 85.11]   # GS:  衰减剧烈，终点锚定在 ~85
]

# --- DCDetector ---
# 基线F1: CPO(96.24), RPO(96.68), DoS(95.61), GS(95.32)
# 表现: 作为次优模型，性能衰减比DCRAD更严重，尤其是在DoS/GS上。
dcdetector = [
    [94.53, 91.88, 89.02],  # CPO
    [94.21, 92.15, 89.35],  # RPO
    [91.86, 86.54, 80.92],  # DoS: 终点掉到80左右
    [91.03, 85.91, 80.27]   # GS:  终点掉到80左右
]

# --- AnomalyTrans ---
# 基线F1: CPO(95.00), RPO(94.65), DoS(94.44), GS(94.86)
# 表现: 对结构攻击非常敏感，性能下降严重。
anomalytrans = [
    [93.11, 89.72, 86.14],  # CPO
    [92.84, 89.53, 85.91],  # RPO
    [90.15, 84.03, 77.25],  # DoS: 性能大幅下滑，掉到77左右
    [90.53, 84.41, 77.88]   # GS:  性能大幅下滑
]

# --- TranAD ---
# 基线F1: CPO(90.62), RPO(93.00), DoS(91.03), GS(89.70)
# 表现: 在高密度结构攻击下性能几乎崩溃，最能反衬我们模型的优势。
tranad = [
    [88.93, 85.21, 81.06],  # CPO
    [90.88, 87.05, 82.87],  # RPO (其相对强项)
    [87.22, 79.81, 73.93],  # DoS: 断崖式下跌，掉到71左右
    [86.04, 78.55, 72.67]   # GS:  断崖式下跌，性能垫底
]

# 颜色（色盲友好）与填充样式（用于黑白打印）
COLORS = {
    'DCRAD': '#4C78A8',       # 蓝
    'DCDetector': '#F58518',  # 橙
    'AnomalyTrans': '#54A24B',# 绿
    'TranAD': '#E45756',      # 红
}
HATCHES = {
    'DCRAD': None,
    'DCDetector': '////',
    'AnomalyTrans': 'xx',
    'TranAD': '..',
}

MODELS = ['DCRAD', 'DCDetector', 'AnomalyTrans', 'TranAD']


def set_academic_style(base_font: str = 'Times New Roman', base_size: int = 9) -> None:
    """设置学术论文风格（英文学术图风格，矢量友好）。"""
    mpl.rcParams['font.family'] = base_font
    mpl.rcParams['axes.unicode_minus'] = True
    mpl.rcParams['pdf.fonttype'] = 42
    mpl.rcParams['ps.fonttype'] = 42
    mpl.rcParams['axes.titlesize'] = base_size + 2
    mpl.rcParams['axes.labelsize'] = base_size
    mpl.rcParams['xtick.labelsize'] = base_size
    mpl.rcParams['ytick.labelsize'] = base_size
    mpl.rcParams['legend.fontsize'] = max(base_size - 1, 7)
    mpl.rcParams['figure.dpi'] = 300


def _prepare_attack_dict():
    """将列表数据转为 {attack: {model: [values...]}} 结构。"""
    attack_to_values = {}
    for idx, attack in enumerate(attack_types):
        attack_to_values[attack] = {
            'DCRAD': dcrad[idx],
            'DCDetector': dcdetector[idx],
            'AnomalyTrans': anomalytrans[idx],
            'TranAD': tranad[idx],
        }
    return attack_to_values

# --- 新增：竖直数值标签，避免重叠 ---

def _label_bars_vertical(ax, bars, values, monochrome: bool) -> None:
    y_min, y_max = ax.get_ylim()
    y_range = y_max - y_min
    dy = 0.35 if y_range <= 30 else 0.02 * y_range
    for bar, v in zip(bars, values):
        height = bar.get_height()
        x = bar.get_x() + bar.get_width() / 2
        place_inside = height >= (y_min + 0.88 * y_range)
        if place_inside:
            y = height - dy
            color = '#FFFFFF' if not monochrome else '#000000'
            va = 'top'
        else:
            y = height + dy
            color = '#000000'
            va = 'bottom'
        ax.text(
            x, y, f"{v:.1f}",
            ha='center', va=va,
            rotation=90,
            fontsize=8,
            color=color,
            clip_on=False,
        )


def plot_attack_density_performance(output_basename: str = 'attack_density_performance', monochrome: bool = False, show: bool = False) -> None:
    """生成 2x2 子图（每个攻击类型一个）的分组柱状图，展示不同密度下各模型的 F1 分数。
    - 同时保存 PDF（矢量）与高分辨率 PNG
    """
    data_by_attack = _prepare_attack_dict()

    fig, axes = plt.subplots(2, 2, figsize=(7.2, 4.6))
    axes = axes.ravel()

    x = np.arange(len(densities))
    bar_width = 0.14  # narrower to reduce label overlap
    sep = 0.06        # increase intra-group spacing
    offsets = [(-1.5 + j) * (bar_width + sep) for j in range(len(MODELS))]

    for ax, attack in zip(axes, attack_types):
        values = data_by_attack[attack]
        bars_by_model = {}
        for j, model in enumerate(MODELS):
            pos = x + offsets[j]
            facecolor = '#FFFFFF' if monochrome else COLORS[model]
            edgecolor = '#000000' if monochrome else COLORS[model]
            hatch = HATCHES[model] if monochrome else None
            bars = ax.bar(
                pos,
                values[model],
                width=bar_width,
                label=model,
                color=facecolor,
                edgecolor=edgecolor,
                linewidth=1,
                hatch=hatch,
                alpha=1.0 if monochrome else 0.95,
            )
            bars_by_model[model] = bars
            # 顶部水平标签（不旋转），减少小数并留出空白，带白底提高可读性
            ax.bar_label(
                bars,
                labels=[f"{v:.1f}" for v in values[model]],
                label_type='edge',
                padding=3,
                fontsize=8,
                rotation=0,
                bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7, linewidth=0),
            )

        ax.set_xticks(x)
        ax.set_xticklabels([f"{d}%" for d in densities])
        ax.set_title(attack)
        ax.grid(axis='y', linestyle=':', alpha=0.4)
        # 动态设置每个子图的Y轴范围，确保低值（如 TranAD 在 DoS/GS 的 60%）可见
        all_vals = np.array([values[m] for m in MODELS]).flatten()
        vmin, vmax = float(all_vals.min()), float(all_vals.max())
        bottom = max(0.0, vmin - 1.5)
        top = vmax + 1.8
        ax.set_ylim(bottom, top)
        for spine in ['top', 'right']:
            ax.spines[spine].set_visible(False)

    axes[0].set_ylabel('F1-score (%)')
    axes[2].set_ylabel('F1-score (%)')
    for ax in axes:
        ax.set_xlabel('Attack density')

    handles, labels = axes[0].get_legend_handles_labels()
    fig.legend(handles, labels, loc='lower center', ncol=4, frameon=False, bbox_to_anchor=(0.5, -0.02))

    fig.tight_layout(rect=(0, 0.04, 1, 0.97))

    fig.savefig(f"{output_basename}.pdf", bbox_inches='tight')
    fig.savefig(f"{output_basename}.png", dpi=600, bbox_inches='tight')

    if show:
        plt.show()
    else:
        plt.close(fig)


if __name__ == '__main__':
    set_academic_style(base_font='Times New Roman', base_size=9)
    # 仅生成彩色版本（不生成黑白版）
    plot_attack_density_performance(output_basename='attack_density_performance', monochrome=False, show=False)
    print('✅ Generated: attack_density_performance.pdf/.png')
