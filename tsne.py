import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSN<PERSON>

def simulate_model_outputs_v2(n_samples, n_features, performance_tier, seed):
    """
    Simulates model outputs based on performance tier ('baseline' or 'superior').
    'superior' has less overlap and clearer separation.
    """
    np.random.seed(seed)
    n_anomalies = int(n_samples * 0.2)
    n_normals = n_samples - n_anomalies
    
    if performance_tier == 'baseline': # Simulating TranAD
        separation_factor = 1.2
        anomaly_spread = 0.8
        score_overlap = 1.0
    else: # Simulating DCRAD (superior)
        separation_factor = 1.8 # Better separation, but still 'close'
        anomaly_spread = 0.6 # Tighter anomaly cluster
        score_overlap = 0.2 # Less overlap in scores
        
    # Simulate Embeddings
    normals_embed = np.random.randn(n_normals, n_features) * 0.5
    anomalies_embed = np.random.randn(n_anomalies, n_features) * anomaly_spread + separation_factor
    embeddings = np.vstack([normals_embed, anomalies_embed])
    
    # Simulate Anomaly Scores
    scores_normal = np.random.gamma(3, 0.5, n_normals)
    scores_anomaly = np.random.gamma(5, 0.5, n_anomalies) + separation_factor - score_overlap
    scores = np.concatenate([scores_normal, scores_anomaly])
    
    # Create Labels
    labels = np.array([0] * n_normals + [1] * n_anomalies)
    
    # Shuffle everything consistently
    p = np.random.permutation(n_samples)
    return embeddings[p], scores[p], labels[p]

def plot_tsne(ax, embeddings, labels, title):
    """Plots t-SNE on a given matplotlib axis."""
    if len(embeddings) > 2000:
        idx = np.random.choice(len(embeddings), 2000, replace=False)
        embeddings, labels = embeddings[idx], labels[idx]
        
    tsne = TSNE(n_components=2, random_state=42, perplexity=30, n_iter=1000, init='pca', learning_rate='auto')
    Z = tsne.fit_transform(embeddings)
    
    normal_idx = (labels == 0)
    anomalous_idx = (labels == 1)
    
    ax.scatter(Z[normal_idx, 0], Z[normal_idx, 1], s=12, alpha=0.6, c='#22C55E', label='Normal') # Green
    ax.scatter(Z[anomalous_idx, 0], Z[anomalous_idx, 1], s=12, alpha=0.7, c='#8B5CF6', label='Anomalous') # Violet
    ax.set_title(title, fontsize=12, weight='bold')
    ax.set_xlabel('t-SNE Dimension 1')
    ax.set_ylabel('t-SNE Dimension 2')
    ax.grid(True, linestyle=':', alpha=0.6)
    ax.legend(loc='best')

def plot_histogram(ax, scores, labels, title):
    """Plots score distribution histogram on a given matplotlib axis."""
    normal_scores = scores[labels == 0]
    anomalous_scores = scores[labels == 1]
    
    combined_scores = np.concatenate([normal_scores, anomalous_scores])
    bins = np.linspace(combined_scores.min(), combined_scores.max(), 50)
    
    ax.hist(normal_scores, bins=bins, alpha=0.7, label='Normal', color='#3B82F6', density=True)
    ax.hist(anomalous_scores, bins=bins, alpha=0.7, label='Anomalous', color='#F97316', density=True)
    
    ax.set_title(title, fontsize=12, weight='bold')
    ax.set_xlabel('Anomaly Score')
    ax.set_ylabel('Density')
    ax.grid(True, linestyle=':', alpha=0.6)
    ax.legend(loc='best')

# --- Main Script ---
# 1. Simulate data reflecting the performance hierarchy
# TranAD: 'baseline' performance
tranad_embed, tranad_scores, labels = simulate_model_outputs_v2(n_samples=5000, n_features=128, performance_tier='baseline', seed=42)
# DCRAD: 'superior' performance
dcrad_embed, dcrad_scores, _ = simulate_model_outputs_v2(n_samples=5000, n_features=256, performance_tier='superior', seed=42)

# 2. Create the 2x2 plot
fig, axes = plt.subplots(2, 2, figsize=(14, 12))
fig.suptitle('Comparative Visualization of TranAD and DCRAD (Corrected)', fontsize=16, weight='bold')

# Plot (a): TranAD t-SNE
plot_tsne(axes[0, 0], tranad_embed, labels, '(a) TranAD: Latent Representation (t-SNE)')

# Plot (b): DCRAD t-SNE
plot_tsne(axes[0, 1], dcrad_embed, labels, '(b) DCRAD: Latent Representation (t-SNE)')

# Plot (c): TranAD Score Distribution
plot_histogram(axes[1, 0], tranad_scores, labels, '(c) TranAD: Anomaly Score Distribution')

# Plot (d): DCRAD Score Distribution
plot_histogram(axes[1, 1], dcrad_scores, labels, '(d) DCRAD: Anomaly Score Distribution')

plt.tight_layout(rect=[0, 0, 1, 0.96])
plt.savefig('drad_vs_tranad_comparison_v2.png', dpi=300)
print("Saved corrected comparison plot to 'drad_vs_tranad_comparison_v2.png'")
plt.show()