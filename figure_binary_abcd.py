import argparse
import numpy as np
import matplotlib as mpl
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.manifold import TSNE

# -------------------- Styling --------------------

def set_academic_style(base_font: str = 'Times New Roman', base_size: int = 10) -> None:
    mpl.rcParams['font.family'] = base_font
    mpl.rcParams['axes.unicode_minus'] = True
    mpl.rcParams['pdf.fonttype'] = 42
    mpl.rcParams['ps.fonttype'] = 42
    mpl.rcParams['axes.titlesize'] = base_size + 1
    mpl.rcParams['axes.labelsize'] = base_size
    mpl.rcParams['xtick.labelsize'] = base_size - 1
    mpl.rcParams['ytick.labelsize'] = base_size - 1
    mpl.rcParams['legend.fontsize'] = base_size - 1
    mpl.rcParams['figure.dpi'] = 300

COLOR_NORMAL = '#4c72b0'   # blue
COLOR_ANOM = '#e48759'     # orange

# -------------------- Synthetic data --------------------

def make_stage_embeddings(n_normal: int, n_anom: int, n_features: int, stage: str, rng: np.random.Generator) -> tuple:
    """Return (X, y) for a given stage. y: 0 normal, 1 anomalous.
    Pretext: stronger overlap. Self-supervised: better separation.
    """
    if stage == 'pretext':
        normal = rng.normal(0.0, 1.4, size=(n_normal, n_features))
        anomal = rng.normal(0.6, 1.5, size=(n_anom, n_features))
        # add slight manifold-like correlation
        normal[:, :3] += np.sin(np.linspace(0, 6, n_normal))[:, None]
        anomal[:, :3] += np.cos(np.linspace(0, 6, n_anom))[:, None]
    else:  # self-supervised
        normal = rng.normal(0.0, 0.55, size=(n_normal, n_features))
        anomal = rng.normal(3.5, 0.65, size=(n_anom, n_features))
        anomal[:, :6] += 1.2
    X = np.vstack([normal, anomal])
    y = np.concatenate([np.zeros(n_normal, dtype=int), np.ones(n_anom, dtype=int)])
    return X, y


def tsne_project(X: np.ndarray, random_state: int = 42) -> np.ndarray:
    Xs = StandardScaler().fit_transform(X)
    tsne = TSNE(n_components=2, perplexity=max(5, (len(X) - 1) // 3), max_iter=1500,
                init='pca', learning_rate='auto', random_state=random_state)
    Y = tsne.fit_transform(Xs)
    return Y

# -------------------- Geometry-based 2D synthesis --------------------

def synth_pretext_2d(n_normal: int, n_anom: int, rng: np.random.Generator) -> tuple:
    """Directly synthesize 2D points to mimic reference (a):
    - Normal: loop/ring with tail
    - Anomalous: partially overlapping along loop + a slanted strip at bottom-right
    Returns (Y, y)
    """
    # Normal loop
    t = np.linspace(0, 2*np.pi, n_normal)
    x = 28*np.cos(t) + 6*np.sin(3*t)
    y = 22*np.sin(t)
    noise = rng.normal(0, 2.4, size=(n_normal, 2))
    Yn = np.column_stack([x, y]) + noise
    # Anomalies overlapping along left arc
    k = int(n_anom*0.55)
    t_a = rng.choice(np.linspace(0.8*np.pi, 1.7*np.pi, 400), size=k, replace=True)
    xa = 28*np.cos(t_a) + 6*np.sin(3*t_a) + rng.normal(0, 2.2, size=k)
    ya = 22*np.sin(t_a) + rng.normal(0, 2.2, size=k)
    Ya1 = np.column_stack([xa, ya])
    # Slanted strip at bottom-right
    k2 = n_anom - k
    base_x = rng.uniform(10, 40, size=k2)
    base_y = -55 + 0.25*(base_x-25) + rng.normal(0, 1.8, size=k2)
    Ya2 = np.column_stack([base_x, base_y])
    Ya = np.vstack([Ya1, Ya2])
    Y = np.vstack([Yn, Ya])
    labels = np.concatenate([np.zeros(len(Yn), dtype=int), np.ones(len(Ya), dtype=int)])
    return Y, labels


def synth_cls_2d(n_normal: int, n_anom: int, rng: np.random.Generator) -> tuple:
    """Directly synthesize 2D points to mimic reference (b):
    - Normal: big C-shape ribbon on left
    - Anomalous: separated small clusters on right and a few on the lower-left
    Returns (Y, y)
    """
    # Normal C-shape
    t = np.linspace(-0.2, 1.45*np.pi, n_normal)
    x = -8 + 28*np.cos(t) + 2.0*np.cos(4*t)
    y = 28*np.sin(t) + 2.0*np.sin(3*t)
    Yn = np.column_stack([x, y]) + rng.normal(0, 1.8, size=(n_normal, 2))
    # Anomaly clusters
    k1 = int(n_anom*0.65)
    Ya_right = rng.normal(loc=[35, 35], scale=[4.5, 5.0], size=(k1, 2))
    k2 = n_anom - k1
    Ya_low = rng.normal(loc=[10, -10], scale=[3.0, 3.5], size=(k2, 2))
    Ya = np.vstack([Ya_right, Ya_low])
    Y = np.vstack([Yn, Ya])
    labels = np.concatenate([np.zeros(len(Yn), dtype=int), np.ones(len(Ya), dtype=int)])
    return Y, labels

# -------------------- Plot helpers --------------------

def panel_letter(ax, letter: str):
    ax.text(0.5, -0.20, letter, transform=ax.transAxes, ha='center', va='top', fontsize=11)


def _apply_axes_style(ax):
    ax.set_xlim(-60, 60)
    ax.set_ylim(-60, 60)
    ax.set_xticks(np.arange(-60, 61, 20))
    ax.set_yticks(np.arange(-60, 61, 20))


def _best_corner_for_points(Y: np.ndarray, margin_ratio: float = 0.28) -> str:
    xmin, xmax = -60.0, 60.0
    ymin, ymax = -60.0, 60.0
    w = (xmax - xmin) * margin_ratio
    h = (ymax - ymin) * margin_ratio
    counts = {}
    counts['upper right'] = np.sum((Y[:, 0] > xmax - w) & (Y[:, 1] > ymax - h))
    counts['upper left']  = np.sum((Y[:, 0] < xmin + w) & (Y[:, 1] > ymax - h))
    counts['lower left']  = np.sum((Y[:, 0] < xmin + w) & (Y[:, 1] < ymin + h))
    counts['lower right'] = np.sum((Y[:, 0] > xmax - w) & (Y[:, 1] < ymin + h))
    return min(counts, key=counts.get)


def plot_embedding(ax, Y: np.ndarray, y: np.ndarray, title: str):
    idx_n = (y == 0)
    idx_a = (y == 1)
    ax.scatter(Y[idx_n, 0], Y[idx_n, 1], s=14, c=COLOR_NORMAL, alpha=0.85, linewidths=0.2, edgecolors='white', label='normal')
    ax.scatter(Y[idx_a, 0], Y[idx_a, 1], s=18, c=COLOR_ANOM, alpha=0.85, marker='x', linewidths=0.9, label='anomalous')
    ax.set_title(title)
    ax.set_xlabel('Dim 1')
    ax.set_ylabel('Dim 2')
    _apply_axes_style(ax)
    # Move legend to the right side outside the plot area
    ax.legend(title='label', frameon=True, facecolor='white', edgecolor='0.8',
              loc='center left', bbox_to_anchor=(1, 0.5))


def plot_hist(ax, normal_scores: np.ndarray, anom_scores: np.ndarray, title: str):
    xmin = max(min(normal_scores.min(), anom_scores.min()), 1e-5)
    xmax = max(normal_scores.max(), anom_scores.max())
    bins = np.logspace(np.log10(xmin), np.log10(xmax), 40)
    n_patches = ax.hist(normal_scores, bins=bins, color=COLOR_NORMAL, alpha=0.65, label='normal', log=True)
    a_patches = ax.hist(anom_scores, bins=bins, color=COLOR_ANOM, alpha=0.55, label='anomalous', log=True)
    ax.set_xscale('log')
    ax.set_xlabel('Anomaly Score (log scale)')
    ax.set_ylabel('Number of Samples (log scale)')
    ax.set_title(title)
    # Move legend to the right side outside the plot area
    ax.legend(title='Ground Truth', frameon=True, facecolor='white', edgecolor='0.8',
              loc='center left', bbox_to_anchor=(1, 0.5))


def simulate_scores(stage: str, n_normal: int, n_anom: int, rng: np.random.Generator) -> tuple:
    if stage == 'pretext':
        # Overlapping around ~1e-3 to 1e-2
        normal = rng.lognormal(mean=np.log(2.5e-3), sigma=0.45, size=n_normal)
        anoms = rng.lognormal(mean=np.log(7.0e-3), sigma=0.50, size=n_anom)
    else:
        # Well separated around ~0.4 vs ~1.0
        normal = rng.lognormal(mean=np.log(0.42), sigma=0.10, size=n_normal)
        anoms = rng.lognormal(mean=np.log(0.95), sigma=0.10, size=n_anom)
    normal = np.clip(normal, 1e-5, np.percentile(normal, 99.7))
    anoms = np.clip(anoms, 1e-5, np.percentile(anoms, 99.7))
    return normal, anoms

# -------------------- Main --------------------

def main():
    parser = argparse.ArgumentParser(description='Reproduce a 4-panel figure with Normal vs Anomalous only')
    parser.add_argument('--seed', type=int, default=202410)
    parser.add_argument('--output', type=str, default='figure_binary_abcd')
    parser.add_argument('--n-normal', type=int, default=600)
    parser.add_argument('--n-anom', type=int, default=250)
    parser.add_argument('--features', type=int, default=36)
    parser.add_argument('--mode', choices=['geometry', 'tsne'], default='geometry', help='geometry: direct 2D synthesis to match reference; tsne: project high-d features')
    args = parser.parse_args()

    set_academic_style()
    rng = np.random.default_rng(args.seed)

    if args.mode == 'geometry':
        Y_pre, y_pre = synth_pretext_2d(args.n_normal, args.n_anom, rng)
        Y_cls, y_cls = synth_cls_2d(args.n_normal, args.n_anom, rng)
    else:
        # Stage data in high-d then t-SNE
        X_pre, y_pre = make_stage_embeddings(args.n_normal, args.n_anom, args.features, 'pretext', rng)
        X_cls, y_cls = make_stage_embeddings(args.n_normal, args.n_anom, args.features, 'cls', rng)
        Y_pre = tsne_project(X_pre, random_state=42)
        Y_cls = tsne_project(X_cls, random_state=42)

    # Scores
    n_pre, a_pre = simulate_scores('pretext', args.n_normal * 4, args.n_anom * 3, rng)
    n_cls, a_cls = simulate_scores('cls', args.n_normal * 4, args.n_anom * 3, rng)

    # Layout - increase figure width to accommodate legends on the right
    fig, axes = plt.subplots(2, 2, figsize=(15.0, 5.8))
    axa, axb, axc, axd = axes.ravel()

    plot_embedding(axa, Y_pre, y_pre, 'Test data embeddings after Pretext Stage')
    panel_letter(axa, '(a)')

    plot_embedding(axb, Y_cls, y_cls, 'Test data embeddings after\nSelf-supervised Classification Stage')
    panel_letter(axb, '(b)')

    plot_hist(axc, n_pre, a_pre, 'Histogram of anomaly score after\nPretext Stage')
    panel_letter(axc, '(c)')

    plot_hist(axd, n_cls, a_cls, 'Histogram of anomaly score after\nSelf-supervised Classification Stage')
    panel_letter(axd, '(d)')

    # Adjust layout to provide space for legends on the right
    fig.tight_layout()
    plt.subplots_adjust(right=0.85)
    fig.savefig(f'{args.output}.png', dpi=600, bbox_inches='tight')
    fig.savefig(f'{args.output}.pdf', bbox_inches='tight')
    print(f'✅ Saved: {args.output}.png/.pdf')


if __name__ == '__main__':
    main() 