import argparse
import numpy as np
import matplotlib as mpl
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse
from sklearn.preprocessing import StandardScaler
from sklearn.manifold import TSNE, trustworthiness
from sklearn.metrics import silhouette_score, davies_bouldin_score, calinski_harabasz_score, roc_auc_score
from sklearn.neighbors import KernelDensity

try:
    import umap.umap_ as umap
    HAS_UMAP = True
except Exception:
    HAS_UMAP = False

# ---------- Styling ----------

def set_academic_style(base_font: str = 'Times New Roman', base_size: int = 11) -> None:
    mpl.rcParams['font.family'] = base_font
    mpl.rcParams['axes.unicode_minus'] = True
    mpl.rcParams['pdf.fonttype'] = 42
    mpl.rcParams['ps.fonttype'] = 42
    mpl.rcParams['axes.titlesize'] = base_size + 2
    mpl.rcParams['axes.labelsize'] = base_size
    mpl.rcParams['xtick.labelsize'] = base_size - 1
    mpl.rcParams['ytick.labelsize'] = base_size - 1
    mpl.rcParams['legend.fontsize'] = base_size - 1
    mpl.rcParams['figure.dpi'] = 300

CLASS_NAMES = ['Normal', 'CPO', 'RPO']
CLASS_COLORS = ['#1f77b4', '#d62728', '#2ca02c']  # 蓝、红、绿
CLASS_MARKERS = ['o', 'x', '^']

# ---------- Synthetic data ----------

def generate_latent_space_embeddings(n_samples_per_class=160, n_features=48, rng=None):
    if rng is None:
        rng = np.random.default_rng(42)
    labels = np.concatenate([
        np.zeros(n_samples_per_class),
        np.ones(n_samples_per_class),
        np.full(n_samples_per_class, 2),
    ])
    # DCRAD: three tight, well-separated clusters
    d_normal = rng.normal(0.0, 0.4, size=(n_samples_per_class, n_features))
    d_cpo = rng.normal(0.0, 0.4, size=(n_samples_per_class, n_features)) + 5.0
    d_rpo = rng.normal(0.0, 0.4, size=(n_samples_per_class, n_features))
    d_rpo[:, :10] -= 5.0
    dcrad = np.vstack([d_normal, d_cpo, d_rpo])
    # TranAD: overlapping clusters
    t_normal = rng.normal(0.0, 1.4, size=(n_samples_per_class, n_features))
    t_cpo = rng.normal(6.0, 1.8, size=(n_samples_per_class, n_features))
    t_rpo = rng.normal(0.0, 1.4, size=(n_samples_per_class, n_features))
    t_rpo[:, :5] += 2.0
    tranad = np.vstack([t_normal, t_cpo, t_rpo])
    return tranad, dcrad, labels


def simulate_anomaly_scores(model: str, n_normal=4000, n_anom=1600, rng=None):
    if rng is None:
        rng = np.random.default_rng(123)
    if model.lower() == 'dcrad':
        # Better separation
        normal = rng.lognormal(mean=np.log(0.10), sigma=0.35, size=n_normal)
        anoms = rng.lognormal(mean=np.log(0.85), sigma=0.35, size=n_anom)
    else:  # tranad
        # More overlap
        normal = rng.lognormal(mean=np.log(0.22), sigma=0.45, size=n_normal)
        anoms = rng.lognormal(mean=np.log(0.55), sigma=0.50, size=n_anom)
    # clip extreme tails for cleaner plot
    normal = np.clip(normal, 1e-4, np.percentile(normal, 99.9))
    anoms = np.clip(anoms, 1e-4, np.percentile(anoms, 99.9))
    return normal, anoms

# ---------- Embedding utilities ----------

def compute_embedding(X: np.ndarray, labels: np.ndarray, method='tsne', perplexity=35, max_iter=2000,
                      n_neighbors=25, min_dist=0.08, random_state=42):
    Xs = StandardScaler().fit_transform(X)
    if method == 'umap' and HAS_UMAP:
        reducer = umap.UMAP(n_components=2, n_neighbors=n_neighbors, min_dist=min_dist,
                            metric='euclidean', random_state=random_state)
        Y = reducer.fit_transform(Xs)
        used = 'UMAP'
    else:
        max_perp = max(5, (len(Xs) - 1) // 3)
        perp = int(min(perplexity, max_perp))
        tsne = TSNE(n_components=2, perplexity=perp, max_iter=max_iter, init='pca',
                    learning_rate='auto', random_state=random_state)
        Y = tsne.fit_transform(Xs)
        used = 't-SNE'
    metrics = {
        'Trust': float(trustworthiness(Xs, Y, n_neighbors=5)),
        'Sil': float(silhouette_score(Y, labels)),
        'DB': float(davies_bouldin_score(Y, labels)),
        'CH': float(calinski_harabasz_score(Y, labels)),
    }
    return Y, metrics, used


def add_density_contours(ax, Y: np.ndarray, color: str, levels: int = 3):
    if Y.shape[0] < 20:
        return
    x, y = Y[:, 0], Y[:, 1]
    xmin, xmax = np.min(x), np.max(x)
    ymin, ymax = np.min(y), np.max(y)
    dx, dy = xmax - xmin, ymax - ymin
    if dx <= 0 or dy <= 0:
        return
    pad_x, pad_y = 0.1 * dx, 0.1 * dy
    xx = np.linspace(xmin - pad_x, xmax + pad_x, 80)
    yy = np.linspace(ymin - pad_y, ymax + pad_y, 80)
    XX, YY = np.meshgrid(xx, yy)
    grid = np.vstack([XX.ravel(), YY.ravel()]).T
    std = max(np.std(x), np.std(y))
    bw = max(0.15 * std, 1e-2)
    kde = KernelDensity(bandwidth=bw, kernel='gaussian')
    kde.fit(Y)
    Z = np.exp(kde.score_samples(grid)).reshape(XX.shape)
    ax.contour(XX, YY, Z, levels=levels, colors=[color], alpha=0.22, linewidths=0.9)


def draw_chi2_ellipse(ax, Y: np.ndarray, color: str, conf: float = 0.95, alpha: float = 0.10):
    if Y.shape[0] < 4:
        return
    cov = np.cov(Y[:, 0], Y[:, 1])
    vals, vecs = np.linalg.eigh(cov)
    order = vals.argsort()[::-1]
    vals, vecs = vals[order], vecs[:, order]
    angle = np.degrees(np.arctan2(*vecs[:, 0][::-1]))
    chi2 = {0.90: 4.605, 0.95: 5.991, 0.99: 9.210}[0.95]
    w, h = 2 * np.sqrt(np.maximum(vals, 1e-10) * chi2)
    ell = Ellipse((np.mean(Y[:, 0]), np.mean(Y[:, 1])), width=w, height=h, angle=angle,
                  facecolor=color, edgecolor=color, alpha=alpha, linewidth=1.0)
    ax.add_patch(ell)


def scatter_panel(ax, Y: np.ndarray, labels: np.ndarray, title: str):
    # contours first
    for i, color in enumerate(CLASS_COLORS):
        add_density_contours(ax, Y[labels == i], color)
    # points & centroids
    for i, (name, color, marker) in enumerate(zip(CLASS_NAMES, CLASS_COLORS, CLASS_MARKERS)):
        idx = (labels == i)
        pts = Y[idx]
        kw = dict(c=color, marker=marker, s=28, alpha=0.9)
        if marker == 'x':
            kw['linewidths'] = 1.2
        else:
            kw['linewidths'] = 0.4
            kw['edgecolors'] = 'white'
        ax.scatter(pts[:, 0], pts[:, 1], label=name, **kw)
        if pts.shape[0] > 5:
            draw_chi2_ellipse(ax, pts, color)
            cx, cy = np.mean(pts[:, 0]), np.mean(pts[:, 1])
            ax.scatter([cx], [cy], c=color, marker='*', s=110, edgecolors='black', linewidths=0.6, zorder=4)
    ax.set_title(title)
    ax.set_xticks([])
    ax.set_yticks([])
    for s in ax.spines.values():
        s.set_edgecolor('0.6')
        s.set_linewidth(1.0)
    ax.set_aspect('equal', adjustable='datalim')

# ---------- Score distribution ----------

def score_hist_panel(ax, normal: np.ndarray, anoms: np.ndarray, title: str, color='#4C78A8', color_anom='#E45756'):
    # Use log-spaced bins to capture tails
    xmin = max(min(normal.min(), anoms.min()), 1e-4)
    xmax = max(normal.max(), anoms.max())
    bins = np.logspace(np.log10(xmin), np.log10(xmax), 45)

    ax.hist(normal, bins=bins, color=color, alpha=0.65, label='normal', density=False)
    ax.hist(anoms, bins=bins, color=color_anom, alpha=0.55, label='anomalous', density=False)

    # Overplot ratio curve (TPR-like proxy)
    n_hist, _ = np.histogram(normal, bins=bins)
    a_hist, _ = np.histogram(anoms, bins=bins)
    ratio = a_hist / np.maximum(n_hist + a_hist, 1)
    centers = np.sqrt(bins[:-1] * bins[1:])
    ax2 = ax.twinx()
    ax2.plot(centers, ratio, color='#333333', linewidth=1.6, linestyle='--', label='anomaly fraction')
    ax2.set_ylabel('Anomaly fraction', color='#333333')
    ax2.tick_params(axis='y', colors='#333333')

    # AUROC (simple separability score)
    y_true = np.concatenate([np.zeros_like(normal), np.ones_like(anoms)])
    y_score = np.concatenate([normal, anoms])
    auroc = roc_auc_score(y_true, y_score)

    ax.set_xscale('log')
    ax.set_xlabel('Anomaly score (log)')
    ax.set_ylabel('Count')
    ax.set_title(f"{title}  |  AUROC={auroc:.2f}")
    ax.grid(True, which='both', linestyle=':', alpha=0.35)

# ---------- Main ----------

def main():
    parser = argparse.ArgumentParser(description='Generate 4-panel figure: latent spaces and anomaly score distributions')
    parser.add_argument('--algo', choices=['tsne', 'umap'], default='tsne')
    parser.add_argument('--seed', type=int, default=202409)
    parser.add_argument('--output', type=str, default='figure_abcd')
    args = parser.parse_args()

    rng = np.random.default_rng(args.seed)
    set_academic_style()

    # data
    tranad, dcrad, y = generate_latent_space_embeddings(rng=rng)

    # embeddings
    Y_d, m_d, used = compute_embedding(dcrad, y, method=args.algo)
    Y_t, m_t, _ = compute_embedding(tranad, y, method=args.algo)

    # figure canvas
    fig, axes = plt.subplots(2, 2, figsize=(12.5, 6.4))
    axa, axb, axc, axd = axes.ravel()

    # unify limits for comparability
    xmin = min(Y_d[:, 0].min(), Y_t[:, 0].min())
    xmax = max(Y_d[:, 0].max(), Y_t[:, 0].max())
    ymin = min(Y_d[:, 1].min(), Y_t[:, 1].min())
    ymax = max(Y_d[:, 1].max(), Y_t[:, 1].max())
    dx, dy = xmax - xmin, ymax - ymin
    pad_x, pad_y = 0.05 * dx, 0.05 * dy
    xlim = (xmin - pad_x, xmax + pad_x)
    ylim = (ymin - pad_y, ymax + pad_y)

    # (a) DCRAD
    scatter_panel(axa, Y_d, y, title=f"(a) DCRAD {used}")
    axa.set_xlim(xlim); axa.set_ylim(ylim)
    # (b) TranAD
    scatter_panel(axb, Y_t, y, title=f"(b) TranAD {used}")
    axb.set_xlim(xlim); axb.set_ylim(ylim)

    # (c) DCRAD anomaly score
    normal_d, anom_d = simulate_anomaly_scores('dcrad', rng=rng)
    score_hist_panel(axc, normal_d, anom_d, title='(c) DCRAD score distribution', color='#4C78A8', color_anom='#E45756')

    # (d) TranAD anomaly score
    normal_t, anom_t = simulate_anomaly_scores('tranad', rng=rng)
    score_hist_panel(axd, normal_t, anom_t, title='(d) TranAD score distribution', color='#54A24B', color_anom='#E45756')

    # shared legend for latent plots
    handles = [plt.Line2D([], [], color=CLASS_COLORS[i], marker=CLASS_MARKERS[i], linestyle='None', markersize=6,
                           label=CLASS_NAMES[i]) for i in range(len(CLASS_NAMES))]
    fig.legend(handles=handles, loc='lower center', bbox_to_anchor=(0.5, -0.02), ncol=3, frameon=False)

    fig.suptitle('Latent Space and Anomaly Score Comparison (Synthetic)', y=0.99)
    fig.tight_layout(rect=(0, 0.03, 1, 0.97))

    fig.savefig(f"{args.output}.png", dpi=600, bbox_inches='tight')
    fig.savefig(f"{args.output}.pdf", bbox_inches='tight')
    print(f"✅ Saved: {args.output}.png/.pdf")


if __name__ == '__main__':
    main() 